import request from '@/utils/request'

// 查询物料领取记录列表
export function listMaterial_receive_log(query) {
  return request({
    url: '/bizz/material_receive_log/list',
    method: 'get',
    params: query
  })
}

// 查询物料领取记录详细
export function getMaterial_receive_log(recordId) {
  return request({
    url: '/bizz/material_receive_log/' + recordId,
    method: 'get'
  })
}

// 新增物料领取记录
export function addMaterial_receive_log(data) {
  return request({
    url: '/bizz/material_receive_log',
    method: 'post',
    data: data
  })
}

// 修改物料领取记录
export function updateMaterial_receive_log(data) {
  return request({
    url: '/bizz/material_receive_log',
    method: 'put',
    data: data
  })
}

// 删除物料领取记录
export function delMaterial_receive_log(recordId) {
  return request({
    url: '/bizz/material_receive_log/' + recordId,
    method: 'delete'
  })
}
