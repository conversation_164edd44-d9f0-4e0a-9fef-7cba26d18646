import request from '@/utils/request'

// 查询物料信息列表
export function listMaterial_info(query) {
  return request({
    url: '/bizz/material_info/list',
    method: 'get',
    params: query
  })
}

// 查询物料信息详细
export function getMaterial_info(materialId) {
  return request({
    url: '/bizz/material_info/' + materialId,
    method: 'get'
  })
}

// 新增物料信息
export function addMaterial_info(data) {
  return request({
    url: '/bizz/material_info',
    method: 'post',
    data: data
  })
}

// 修改物料信息
export function updateMaterial_info(data) {
  return request({
    url: '/bizz/material_info',
    method: 'put',
    data: data
  })
}

// 删除物料信息
export function delMaterial_info(materialId) {
  return request({
    url: '/bizz/material_info/' + materialId,
    method: 'delete'
  })
}
