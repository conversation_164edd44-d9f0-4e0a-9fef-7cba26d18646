<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物料名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['bizz:material_info:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['bizz:material_info:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['bizz:material_info:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="material_infoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="物料用途说明" align="center" prop="materialDesc" />
      <el-table-column label="使用方法说明" align="center" prop="instruction" />
      <el-table-column label="库存总数" align="center" prop="totalQuantity" />
      <el-table-column label="是否首次免费领取" align="center" prop="firstFreeFlag">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.firstFreeFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="允许首免的角色" align="center" prop="freeRoles" >
        <template #default="scope">
          <span v-if="scope.row.freeRoles">
            {{ scope.row.freeRoles.split(',').map(role => getRoleLabel(role.trim())).join(', ') }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['bizz:material_info:edit']"
          >修改</el-button>
          <el-button
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['bizz:material_info:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改物料信息对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="material_infoRef" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="form.materialName" placeholder="请输入物料名称" />
        </el-form-item>
        <el-form-item label="物料用途说明" prop="materialDesc">
          <el-input v-model="form.materialDesc" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="使用方法说明" prop="instruction">
          <el-input v-model="form.instruction" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="库存总数" prop="totalQuantity">
          <el-input v-model="form.totalQuantity" placeholder="请输入库存总数" />
        </el-form-item>
        <el-form-item label="是否首次免费领取" prop="firstFreeFlag">
          <el-select v-model="form.firstFreeFlag" placeholder="请选择是否首次免费领取">
            <el-option
              v-for="dict in sys_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="允许首免的角色" prop="freeRoles">
          <el-select
              v-model="form.freeRolesArray"
              multiple
              placeholder="请选择允许首免的角色"
              style="width: 100%"
          >
            <el-option
                v-for="dict in sys_user_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { listMaterial_info, getMaterial_info, delMaterial_info, addMaterial_info, updateMaterial_info } from "@/api/bizz/material_info";

const { proxy } = getCurrentInstance();
const { sys_yes_no,sys_user_type } = proxy.useDict('sys_yes_no', 'sys_user_type');

const material_infoList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    materialName: null,
  },
  rules: {
    materialName: [
      { required: true, message: "物料名称不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询物料信息列表 */
function getList() {
  loading.value = true;
  listMaterial_info(queryParams.value).then(response => {
    material_infoList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    materialId: null,
    materialName: null,
    materialDesc: null,
    instruction: null,
    totalQuantity: null,
    firstFreeFlag: null,
    freeRoles: null,
    freeRolesArray: [],
    createTime: null,
    updateTime: null
  };
  proxy.resetForm("material_infoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.materialId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  getMaterial_info().then((res) => {
    open.value = true;
  })
  title.value = "添加物料信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _materialId = row.materialId || ids.value
  getMaterial_info(_materialId).then(response => {
    form.value = response.data;
    form.value.freeRolesArray =  form.value.freeRoles ? form.value.freeRoles.split(',') : [];
    open.value = true;
    title.value = "修改物料信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["material_infoRef"].validate(valid => {
    if (valid) {

      // 处理 freeRolesIds
      if (form.value.freeRolesArray && form.value.freeRolesArray.length > 0) {
        form.value.freeRoles = form.value.freeRolesArray.join(',');
      } else {
        form.value.freeRoles = '';
      }
      if (form.value.materialId != null) {
        updateMaterial_info(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMaterial_info(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _materialIds = row.materialId || ids.value;
  proxy.$modal.confirm('是否确认删除物料信息编号为"' + _materialIds + '"的数据项？').then(function() {
    return delMaterial_info(_materialIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('bizz/material_info/export', {
    ...queryParams.value
  }, `material_info_${new Date().getTime()}.xlsx`)
}
function getRoleLabel(role) {
  const roleMap = {
    'general': '普通用户',
    'aider': '急救员',
    'mentor': '急救导师',
    'disciple': '弟子',
    'founder': '创始人',
    'union_founder': '联合创始人',
  };
  return roleMap[role] || role;
}
getList();
</script>
