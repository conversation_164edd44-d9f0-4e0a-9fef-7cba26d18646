<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="realName">
        <el-input
            v-model="queryParams.realName"
            placeholder="请输入姓名"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="身份证号" prop="idCard">
        <el-input
            v-model="queryParams.idCard"
            placeholder="请输入身份证号"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phoneNumber">
        <el-input
            v-model="queryParams.phoneNumber"
            placeholder="请输入手机号"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="性别" prop="sex" label-width="auto">
        <el-select v-model="queryParams.sex" placeholder="请选择性别" clearable  style="width: 200px">
          <el-option
              v-for="dict in sys_user_sex"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户类型" prop="userType">
        <el-select v-model="queryParams.userType" placeholder="请选择用户类型" clearable  style="width: 200px">
          <el-option
              v-for="dict in sys_user_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="邀请人" prop="inviter">
        <el-input
            v-model="queryParams.inviter"
            placeholder="请输入邀请人"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="注册时间" style="width: 308px">
        <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
        >修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="User"
            :disabled="!allSelectedStatus2"
            @click="openUserTypeDialog"
        >提升用户类型</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="info"
            plain
            icon="UserFilled"
            :disabled="multiple"
            @click="openInviterDialog"
        >变更成交人</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="微信昵称" align="center" prop="nickName" show-overflow-tooltip fixed="left"/>
      <el-table-column label="姓名" align="center" prop="realName" show-overflow-tooltip fixed="left"/>
      <el-table-column label="性别" align="center" prop="sex">
        <template #default="scope">
          <dict-tag :options="sys_user_sex" :value="scope.row.sex" />
        </template>
      </el-table-column>
      <el-table-column label="身份证号" align="center" prop="idCard" show-overflow-tooltip/>
      <el-table-column label="手机号" align="center" prop="phoneNumber" show-overflow-tooltip />
      <el-table-column label="用户类型" align="center" prop="userType">
        <template #default="scope">
          <template v-if="scope.row.userType">
            <dict-tag
                v-for="type in scope.row.userType.split(',')"
                :key="type.trim()"
                :options="sys_user_type"
                :value="type.trim()"
                style="margin-right: 4px; margin-bottom: 2px;"
            />
          </template>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="邀请人" align="center" prop="inviter" show-overflow-tooltip />
      <el-table-column label="邀请人ID" align="center" prop="inviterId" />
      <el-table-column label="成交人" align="center" prop="dealInviter" show-overflow-tooltip />
      <el-table-column label="成交人ID" align="center" prop="dealInviterId" />
      <el-table-column label="注册时间" align="center" prop="registerTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.registerTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="280">
        <template #default="scope">
          <el-button link type="primary" text @click="handleDelete(scope.row)" size="small">删除</el-button>
          <el-button link type="primary" text @click="openUserTypeDialog(scope.row)" size="small">提升用户类型</el-button>
          <el-button link type="warning" text @click="openInviterDialog(scope.row)" size="small">变更成交人</el-button>
          <el-button link type="success" text @click="openRecommendListDialog(scope.row)" v-if="scope.row.userType" size="small">查看感召人</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改用户对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="userRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="姓名" prop="realName">
          <el-input v-model="form.realName" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select v-model="form.sex" placeholder="请选择性别">
            <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="手机号" prop="phoneNumber">
          <el-input v-model="form.phoneNumber" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="用户类型" prop="userType">
          <el-input v-model="form.userType" placeholder="请输入用户类型" />
        </el-form-item>
        <el-form-item label="邀请人" prop="inviter">
          <el-input v-model="form.inviter" placeholder="请输入邀请人" />
        </el-form-item>
        <el-form-item label="邀请人ID" prop="inviterId">
          <el-input v-model="form.inviterId" placeholder="请输入邀请人ID" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      title="用户类型变更"
      v-model="userTypeDialogVisible"
      width="400px"
      append-to-body
    >
      <el-form>
        <el-form-item label="成交人" label-width="auto" required>
          <RemoteSearchSelect
              v-model="dealInviterSelectValue"
              :remote-method="fetchDealInviterList"
              value-key="userId"
              label-key="label"
              placeholder="请输入姓名或者身份证号"
              :page-size="10"
              @select="handleDealInviterSelect"
              :cache-enabled="false"
          />
        </el-form-item>
        <el-form-item label="用户类型" required>
          <el-select v-model="selectedUserType" multiple placeholder="请选择用户类型">
            <el-option
              v-for="dict in form_sys_user_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="userTypeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUserTypeConfirm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 变更成交人对话框 -->
    <el-dialog
      title="变更成交人"
      v-model="inviterDialogVisible"
      width="400px"
      append-to-body
    >
      <el-form>
        <el-form-item label="新成交人" label-width="auto">
          <RemoteSearchSelect
              ref="newInviterSelectRef"
              v-model="newInviterSelectValue"
              :remote-method="fetchDealInviterList"
              value-key="userId"
              label-key="label"
              placeholder="请输入姓名或者身份证号"
              :page-size="10"
              @select="handleNewInviterSelect"
              :cache-enabled="false"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="inviterDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleInviterConfirm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看感召人对话框 -->
    <el-dialog
      title="感召人列表"
      v-model="recommendListDialogVisible"
      width="800px"
      append-to-body
      draggable
    >
      <el-table v-loading="loading" :data="recommendList">
        <el-table-column label="姓名" align="center" prop="realName" show-overflow-tooltip width="100"/>
        <el-table-column label="注册时间" align="center" prop="registerTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.upgradedTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="recommendListDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {listMemberInfo, updateMemberType, selectMemberList, updateMemberInviter, getRecommendList} from "@/api/bizz/member_info";
import RemoteSearchSelect from "@/components/remote-search-select";
import {addUser, getUser, updateUser} from "@/api/system/user.js";
const { proxy } = getCurrentInstance();
const { sys_user_sex, sys_user_type } = proxy.useDict("sys_user_sex", "sys_user_type");
const {sys_user_type: form_sys_user_type } = proxy.useFilteredDict({sys_user_type: ['general','disciple', 'union_founder']})
const userList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const selectionItems = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const userTypeDialogVisible = ref(false);
const selectedUserType = ref(null);
const dealInviterSelectValue = ref(null);
const inviterDialogVisible = ref(false);
const newInviterSelectValue = ref(null);
const newInviterSelectRef = ref(null);
const recommendListDialogVisible = ref(false);
const recommendList = ref([]);
const currentUserId = ref(null);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    realName: null,
    sex: null,
    idCard: null,
    phoneNumber: null,
    userType: null,
    inviter: null,
    inviterId: null,
  },
  rules: {
    realName: [
      { required: true, message: "姓名不能为空", trigger: "blur" }
    ],
    phoneNumber: [
      { required: true, message: "手机号不能为空", trigger: "blur" },
      { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }
    ],
    idCard: [
      { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: "请输入正确的身份证号码", trigger: "blur" }
    ]
  }
});

const allSelectedStatus2 = computed(() => {
    return ids.value.length > 0 && selectionItems.value.every(item => item.userType === selectionItems.value[0].userType);
});

const { queryParams, form, rules } = toRefs(data);

/** 查询用户列表 */
const getList = () => {
  loading.value = true
  listMemberInfo(queryParams.value)
      .then(({ rows, total:resTotal }) => {
        userList.value = rows
        total.value = resTotal
      })
      .finally(() => loading.value = false)
}

const cancel = () => {
  open.value = false
  reset()
}

const reset = () => {
  form.value = {
    userId: null,
    realName: null,
    sex: null,
    idCard: null,
    phoneNumber: null,
    userType: null,
    inviter: null,
    inviterId: null,
    registerTime: null
  }
  proxy?.resetForm("userRef")
}

const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}

const resetQuery = () => {
  dateRange.value = []
  proxy?.resetForm("queryRef")
  handleQuery()
}

const handleSelectionChange = selection => {
  selectionItems.value = selection
  ids.value = selection.map(item => item.userId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

const handleAdd = () => {
  reset()
  open.value = true
  title.value = "添加用户"
}

const handleUpdate = row => {
  reset()
  getUser(row?.userId || ids.value)
      .then(res => {
        form.value = res.data
        open.value = true
        title.value = "修改用户"
      })
}

const submitForm = () => {
  proxy.$refs.userRef.validate(valid => {
    if (!valid) return

    const submitAction = form.value.userId
        ? updateUser(form.value)
        : addUser(form.value)

    submitAction.then(() => {
      proxy.$modal.msgSuccess(`${form.value.userId ? '修改' : '新增'}成功`)
      open.value = false
      getList()
    })
  })
}

const handleDelete = row => {
  const userIds = row?.userId || ids.value
  proxy.$modal.confirm(`确认删除用户 ${userIds}？`)
      .then(() => delUser(userIds))
      .then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      })
      .catch(() => {})
}

const handleExport = () => {
  proxy.download('bizz/user/export', {
    ...queryParams.value
  }, `user_${Date.now()}.xlsx`)
}

const openUserTypeDialog = row => {
  dealInviterSelectValue.value = null;
  if (row) {
    ids.value = [row.userId]
  }
  if(ids.value.length === 0) {
    proxy.$modal.msgWarning("请选择数据项!")
    return
  }
  selectedUserType.value = null
  userTypeDialogVisible.value = true
}

const handleUserTypeConfirm = () => {
  if (!selectedUserType.value) {
    proxy.$modal.msgWarning("未选择用户类型")
    return
  }

  const getTypeLabels = (typeValues) => {
    if (Array.isArray(typeValues)) {
      // 如果是数组（多选）
      return typeValues.map(value => {
        const option = sys_user_type.value.find(opt => opt.value === value)
        return option ? option.label : value
      })
    } else {
      // 如果是字符串（兼容单选或逗号分割）
      const values = typeValues.split(',').map(v => v.trim())
      return values.map(value => {
        const option = sys_user_type.value.find(opt => opt.value === value)
        return option ? option.label : value
      })
    }
  }

  // 获取类型标签
  const typeLabels = getTypeLabels(selectedUserType.value)
  const typeLabelText = typeLabels.join('、')
  const updateUserTypeValue = Array.isArray(selectedUserType.value)
      ? selectedUserType.value.join(',')
      : selectedUserType.value
  proxy.$modal.confirm(`确认更改用户类型为【${typeLabelText}】？`)
      .then(() => updateMemberType({
        ids: ids.value,
        updateUserType: updateUserTypeValue,
        dealInviterId: dealInviterSelectValue.value
      }))
      .then(() => {
        userTypeDialogVisible.value = false
        getList()
        proxy.$modal.msgSuccess('类型升级成功')
      })
      .catch(() => {})
}


const fetchDealInviterList = async ({ keyWord, pageNum, pageSize }) => {
  return new Promise((resolve) => {
    selectMemberList({
          pageNum,
          pageSize,
          keyWord
        }
    ).then(res =>{
      res.rows.map((row) => {
        row.label = `${row.realName} -- ${row.idCard}`
      })
      let data = {
        list: res.rows,
        total : res.total
      }
      resolve(data)
    })
  })
}
const handleDealInviterSelect = (item) => {
  if (!item) {
    dealInviterSelectValue.value = null;
    return;
  }
  dealInviterSelectValue.value = item.userId;
}

// 打开变更成交人
const openInviterDialog = row => {
  newInviterSelectRef.value?.handleClear();
  ids.value = row?.userId ? [row.userId] : ids.value
  if(ids.value.length === 0) {
    proxy.$modal.msgWarning("请选择数据项!")
    return
  }
  inviterDialogVisible.value = true
}

// 处理新邀请人选择
const handleNewInviterSelect = (item) => {
  if (!item) {
    newInviterSelectValue.value = null;
    return;
  }
  newInviterSelectValue.value = item.userId;
}

// 确认变更成交人
const handleInviterConfirm = () => {
  if (!newInviterSelectValue.value) {
    proxy.$modal.msgWarning("请选择新成交人")
    return
  }

  const userIds = ids.value.join(',')
  proxy.$modal.confirm('确认变更成交人？')
      .then(() => updateMemberInviter(userIds, newInviterSelectValue.value))
      .then(() => {
        inviterDialogVisible.value = false
        getList()
        proxy.$modal.msgSuccess('成交人变更成功')
      })
      .catch(() => {})
}

// 打开感召人列表对话框
const openRecommendListDialog = (row) => {
  if (!row || !row.userId) {
    proxy.$modal.msgWarning("用户信息不完整")
    return
  }
  currentUserId.value = row.userId
  recommendList.value = []
  recommendListDialogVisible.value = true
  getRecommendListData()
}

// 获取感召人列表数据
const getRecommendListData = () => {
  if (!currentUserId.value) return

  loading.value = true
  getRecommendList(currentUserId.value)
    .then(res => {
      recommendList.value = res.rows || res.data.rows || []
    })
    .catch(error => {
      proxy.$modal.msgError("获取感召人列表失败")
      console.error('获取感召人列表失败:', error)
    })
    .finally(() => {
      loading.value = false
    })
}

getList();
</script>
